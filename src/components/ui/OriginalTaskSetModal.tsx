import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, FileText, Headphones, CheckCircle, Clock, Eye } from 'lucide-react'
import { cn } from '../../utils/cn'
import type { TaskSet } from '../../services/task/taskService'
import { QuestionsList } from '../curated/QuestionsList'
import type { Question } from '../../services/curatedService'

interface OriginalTaskSetModalProps {
  open: boolean
  onClose: () => void
  taskSet: TaskSet | null
  tasks: any[]
  loading: boolean
}

const OriginalTaskSetModal: React.FC<OriginalTaskSetModalProps> = ({
  open,
  onClose,
  taskSet,
  tasks,
  loading
}) => {
  if (!open) return null

  const getTaskTypeIcon = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'audio':
        return <Headphones className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getTaskTypeColor = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'single_choice':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
      case 'multiple_choice':
        return 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300'
      case 'image_identification':
        return 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'
      case 'audio_comprehension':
        return 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300'
      case 'word_identification':
        return 'bg-cyan-100 dark:bg-cyan-900/20 text-cyan-700 dark:text-cyan-300'
      default:
        return 'bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-300'
    }
  }

  const formatTaskType = (type: string) => {
    return type?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Unknown'
  }

  const getQuestionText = (task: any) => {
    if (typeof task.question === 'object' && task.question?.text) {
      return task.question.text
    }
    return task.question || 'No question text available'
  }

  // Convert tasks to Question format for QuestionsList component
  const convertTasksToQuestions = (tasks: any[]): Question[] => {
    if (!Array.isArray(tasks)) {
      console.warn('Tasks is not an array:', tasks)
      return []
    }
    return tasks.map(task => ({
      question: {
        text: getQuestionText(task),
        translated_text: typeof task.question === 'object' ? task.question?.translated_text : undefined,
        type: task.type === 'single_choice' ? 'single_choice' as const : 'multiple_choice' as const,
        options: task.options || undefined
      }
    }))
  }

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-4xl max-h-[90vh] bg-background border border-border rounded-xl shadow-2xl overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-border bg-muted/30">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-emerald-100 dark:bg-emerald-900/20">
                <Eye className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-foreground">
                  Original Task Set
                </h2>
                <p className="text-sm text-muted-foreground">
                  {taskSet?.title || 'View original questions and content'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-lg hover:bg-muted transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading original task set...</p>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Task Set Info */}
                {taskSet && (
                  <div className="bg-muted/30 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      {getTaskTypeIcon(taskSet.input_type || '')}
                      <h3 className="font-semibold text-foreground">
                        {taskSet.title || 'Original Task Set'}
                      </h3>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Total Tasks:</span>
                        <p className="font-medium">{taskSet.total_tasks || tasks.length}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Type:</span>
                        <p className="font-medium">{taskSet.input_type || 'Unknown'}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Status:</span>
                        <p className="font-medium capitalize">{taskSet.status || 'Unknown'}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Score:</span>
                        <p className="font-medium">{taskSet.scored || 0}/{taskSet.total_score || 0}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Questions List */}
                <div>
                  <h4 className="font-semibold text-foreground mb-4">
                    Questions ({Array.isArray(tasks) ? tasks.length : 0})
                  </h4>
                  <QuestionsList questions={convertTasksToQuestions(Array.isArray(tasks) ? tasks : [])} />
                </div>

                {tasks.length === 0 && !loading && (
                  <div className="text-center py-12">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No tasks found in this task set.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default OriginalTaskSetModal
