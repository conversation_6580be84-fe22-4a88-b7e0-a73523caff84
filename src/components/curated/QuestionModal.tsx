import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Save, Palette, AlertCircle } from 'lucide-react'
import { cn } from '../../utils/cn'
import { 
  CuratedService, 
  ThemeQuestion, 
  Theme, 
  CreateThemeQuestionRequest, 
  UpdateThemeQuestionRequest 
} from '../../services/curatedService'

interface QuestionModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  editingQuestion?: ThemeQuestion | null
}

interface FormData {
  theme_id: string
  text: string
  text_en: string
}

interface FormErrors {
  theme_id?: string
  text?: string
  text_en?: string
}

/**
 * Question Modal Component
 * Handles creating and editing theme questions
 */
const QuestionModal: React.FC<QuestionModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  editingQuestion
}) => {
  const [formData, setFormData] = useState<FormData>({
    theme_id: '',
    text: '',
    text_en: ''
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [themes, setThemes] = useState<Theme[]>([])
  const [loading, setLoading] = useState(false)
  const [themesLoading, setThemesLoading] = useState(true)

  const isEditing = !!editingQuestion

  // Load themes on mount
  useEffect(() => {
    const fetchThemes = async () => {
      try {
        setThemesLoading(true)
        const response = await CuratedService.getThemes({ limit: 100, is_active: true })
        setThemes(response.data)
      } catch (err) {
        console.error('Error fetching themes:', err)
      } finally {
        setThemesLoading(false)
      }
    }

    if (isOpen) {
      fetchThemes()
    }
  }, [isOpen])

  // Initialize form data when editing
  useEffect(() => {
    if (editingQuestion) {
      setFormData({
        theme_id: editingQuestion.theme_id,
        text: editingQuestion.text,
        text_en: editingQuestion.text_en || ''
      })
    } else {
      setFormData({
        theme_id: '',
        text: '',
        text_en: ''
      })
    }
    setErrors({})
  }, [editingQuestion, isOpen])

  // Handle form field changes
  const handleChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.theme_id.trim()) {
      newErrors.theme_id = 'Please select a theme'
    }

    if (!formData.text.trim()) {
      newErrors.text = 'Original text is required'
    }

    if (!formData.text_en.trim()) {
      newErrors.text_en = 'English text is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      setLoading(true)

      if (isEditing && editingQuestion) {
        const updateData: UpdateThemeQuestionRequest = {
          text: formData.text.trim(),
          text_en: formData.text_en.trim()
        }
        await CuratedService.updateThemeQuestion(editingQuestion._id || editingQuestion.id!, updateData)
      } else {
        const createData: CreateThemeQuestionRequest = {
          theme_id: formData.theme_id,
          text: formData.text.trim(),
          text_en: formData.text_en.trim()
        }
        await CuratedService.createThemeQuestion(createData)
      }

      onSuccess()
      onClose()
    } catch (err) {
      console.error('Error saving question:', err)
      // You could add a toast notification here
      alert('Failed to save question. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Get theme by ID
  const getThemeById = (themeId: string) => {
    return themes.find(t => t._id === themeId || t.id === themeId)
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          className="bg-card border border-border rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-border">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Palette className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-foreground">
                  {isEditing ? 'Edit Question' : 'Create New Question'}
                </h2>
                <p className="text-sm text-muted-foreground">
                  {isEditing ? 'Update the question details' : 'Add a new theme-based question'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Theme Selection */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Theme *
              </label>
              {themesLoading ? (
                <div className="h-10 bg-muted rounded-lg animate-pulse" />
              ) : (
                <div className="relative">
                  <select
                    value={formData.theme_id}
                    onChange={(e) => handleChange('theme_id', e.target.value)}
                    disabled={isEditing} // Can't change theme when editing
                    className={cn(
                      'w-full px-3 py-2 border rounded-lg bg-background text-foreground',
                      'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                      'disabled:opacity-50 disabled:cursor-not-allowed',
                      errors.theme_id ? 'border-red-500' : 'border-border'
                    )}
                  >
                    <option value="">Select a theme...</option>
                    {themes.map((theme) => (
                      <option key={theme._id || theme.id} value={theme._id || theme.id}>
                        {theme.name_en || theme.name} ({theme.category})
                      </option>
                    ))}
                  </select>
                  {errors.theme_id && (
                    <div className="flex items-center gap-1 mt-1 text-red-600 text-sm">
                      <AlertCircle className="w-4 h-4" />
                      {errors.theme_id}
                    </div>
                  )}
                </div>
              )}
              
              {/* Theme Preview */}
              {formData.theme_id && (
                <div className="mt-2">
                  {(() => {
                    const selectedTheme = getThemeById(formData.theme_id)
                    if (!selectedTheme) return null
                    
                    return (
                      <div 
                        className="inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium"
                        style={{
                          backgroundColor: `${selectedTheme.background_color}20`,
                          color: selectedTheme.background_color
                        }}
                      >
                        <Palette className="w-3 h-3" />
                        {selectedTheme.name_en || selectedTheme.name}
                      </div>
                    )
                  })()}
                </div>
              )}
            </div>

            {/* Original Text */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Original Text (Nepali) *
              </label>
              <textarea
                value={formData.text}
                onChange={(e) => handleChange('text', e.target.value)}
                placeholder="Enter the question in Nepali..."
                rows={3}
                className={cn(
                  'w-full px-3 py-2 border rounded-lg bg-background text-foreground resize-none',
                  'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                  'placeholder:text-muted-foreground',
                  errors.text ? 'border-red-500' : 'border-border'
                )}
              />
              {errors.text && (
                <div className="flex items-center gap-1 mt-1 text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.text}
                </div>
              )}
            </div>

            {/* English Text */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                English Text *
              </label>
              <textarea
                value={formData.text_en}
                onChange={(e) => handleChange('text_en', e.target.value)}
                placeholder="Enter the question in English..."
                rows={3}
                className={cn(
                  'w-full px-3 py-2 border rounded-lg bg-background text-foreground resize-none',
                  'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary',
                  'placeholder:text-muted-foreground',
                  errors.text_en ? 'border-red-500' : 'border-border'
                )}
              />
              {errors.text_en && (
                <div className="flex items-center gap-1 mt-1 text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.text_en}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t border-border">
              <button
                type="button"
                onClick={onClose}
                disabled={loading}
                className="px-4 py-2 text-muted-foreground hover:text-foreground border border-border rounded-lg hover:bg-accent transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
              >
                {loading ? (
                  <div className="w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                {isEditing ? 'Update Question' : 'Create Question'}
              </button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default QuestionModal
