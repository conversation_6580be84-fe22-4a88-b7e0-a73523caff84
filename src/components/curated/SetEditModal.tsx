import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import * as Dialog from '@radix-ui/react-dialog'
import {
  X,
  ChevronLeft,
  ChevronRight,
  Loader2,
  AlertCircle,
  Edit3
} from 'lucide-react'
import { ContentSet, CuratedService } from '../../services/curatedService'
import { cn } from '../../utils/cn'
import QuestionEditForm from './QuestionEditForm'

interface SetEditModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  contentSet: ContentSet | null
}

interface TaskData {
  id: string
  type: string
  title: string
  question: {
    text: string
    translated_text: string
    options: Record<string, string>
    options_en: Record<string, string>
    correct_answer_index: number
    answer_hint?: string
  }
  correct_answer: {
    text: string
    index: number
    explanation?: string
  }
  status: string
  difficulty_level: number
}

/**
 * Modal for editing questions in a curated content set
 * Fetches set details and individual task data for editing
 */
const SetEditModal: React.FC<SetEditModalProps> = ({
  open,
  onOpenChange,
  contentSet
}) => {
  const [setDetails, setSetDetails] = useState<any>(null)
  const [tasks, setTasks] = useState<TaskData[]>([])
  const [currentTaskIndex, setCurrentTaskIndex] = useState(0)
  const [loading, setLoading] = useState(false)
  const [loadingTasks, setLoadingTasks] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [saving, setSaving] = useState(false)

  // Fetch set details and tasks when modal opens
  useEffect(() => {
    if (open && contentSet) {
      fetchSetDetails()
    }
  }, [open, contentSet])

  const fetchSetDetails = async () => {
    if (!contentSet) return

    try {
      setLoading(true)
      setError(null)

      // Fetch set details
      const setResponse = await CuratedService.getCuratedSetById(contentSet.id)
      setSetDetails(setResponse.data)

      // Only fetch the first task initially (not all tasks)
      if (setResponse.data.tasks && setResponse.data.tasks.length > 0) {
        await fetchSingleTask(setResponse.data.tasks[0])
      }
    } catch (err) {
      console.error('Error fetching set details:', err)
      setError('Failed to load set details. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const fetchSingleTask = async (taskId: string) => {
    try {
      setLoadingTasks(true)
      const taskResponse = await CuratedService.getCuratedTaskById(taskId)
      const taskData = taskResponse.data

      // Update the tasks array with the fetched task
      setTasks(prev => {
        const newTasks = [...prev]
        const existingIndex = newTasks.findIndex(t => t.id === taskData.id)
        if (existingIndex >= 0) {
          newTasks[existingIndex] = taskData
        } else {
          newTasks.push(taskData)
        }
        return newTasks
      })
    } catch (err) {
      console.error('Error fetching task:', err)
      setError('Failed to load task details. Please try again.')
    } finally {
      setLoadingTasks(false)
    }
  }

  const handleSaveQuestion = async (taskId: string, updatedData: any) => {
    try {
      setSaving(true)
      console.log('Saving question:', taskId, updatedData)

      const response = await CuratedService.updateCuratedTaskQuestion(taskId, updatedData)
      console.log('Save response:', response)

      // Force refresh the current task data (no caching)
      await refreshCurrentTask()
    } catch (err) {
      console.error('Error saving question:', err)
      throw err
    } finally {
      setSaving(false)
    }
  }

  const refreshCurrentTask = async () => {
    if (!setDetails?.tasks || currentTaskIndex < 0) return

    const taskId = setDetails.tasks[currentTaskIndex]
    try {
      setLoadingTasks(true)
      console.log('Refreshing task:', taskId)

      const updatedTaskResponse = await CuratedService.getCuratedTaskById(taskId)
      const updatedTask = updatedTaskResponse.data

      console.log('Refreshed task data:', updatedTask)

      setTasks(prev => {
        const newTasks = prev.filter(t => t.id !== taskId) // Remove old version
        newTasks.push(updatedTask) // Add fresh version
        return newTasks
      })
    } catch (err) {
      console.error('Error refreshing task:', err)
    } finally {
      setLoadingTasks(false)
    }
  }

  const handleClose = () => {
    setSetDetails(null)
    setTasks([])
    setCurrentTaskIndex(0)
    setError(null)
    onOpenChange(false)
  }

  // Navigate to a specific task index and always fetch fresh data
  const navigateToTask = async (index: number) => {
    if (!setDetails?.tasks) return

    setCurrentTaskIndex(index)
    const taskId = setDetails.tasks[index]

    // Always fetch fresh data to avoid cache issues
    console.log('Navigating to task:', index, taskId)
    await fetchSingleTask(taskId)
  }

  const currentTask = tasks.find(t => t.id === setDetails?.tasks?.[currentTaskIndex])
  const hasMultipleTasks = setDetails?.tasks?.length > 1

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-[95vw] max-w-6xl max-h-[90vh] overflow-hidden">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-slate-900 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Edit3 className="w-5 h-5 text-blue-500" />
                  <span className="text-sm text-muted-foreground font-medium">
                    Edit Questions
                  </span>
                </div>
                <div>
                  <Dialog.Title className="text-xl font-semibold text-slate-900 dark:text-white">
                    {setDetails?.title || contentSet?.title}
                  </Dialog.Title>
                  {hasMultipleTasks && (
                    <Dialog.Description className="text-sm text-slate-600 dark:text-slate-400">
                      Question {currentTaskIndex + 1} of {setDetails?.tasks?.length || 0}
                    </Dialog.Description>
                  )}
                </div>
              </div>
              <Dialog.Close onClick={handleClose} className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">
                <X className="w-5 h-5" />
              </Dialog.Close>
            </div>

            {/* Navigation for multiple tasks */}
            {hasMultipleTasks && (
              <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800/50 border-b border-slate-200 dark:border-slate-700">
                <button
                  onClick={() => navigateToTask(Math.max(0, currentTaskIndex - 1))}
                  disabled={currentTaskIndex === 0}
                  className={cn(
                    'flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    currentTaskIndex === 0
                      ? 'text-muted-foreground cursor-not-allowed'
                      : 'text-foreground hover:bg-slate-200 dark:hover:bg-slate-700'
                  )}
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </button>

                <div className="flex items-center gap-2">
                  {setDetails?.tasks?.map((taskId: string, index: number) => (
                    <button
                      key={taskId}
                      onClick={() => navigateToTask(index)}
                      className={cn(
                        'w-8 h-8 rounded-full text-xs font-medium transition-colors',
                        index === currentTaskIndex
                          ? 'bg-blue-500 text-white'
                          : 'bg-slate-200 dark:bg-slate-700 text-muted-foreground hover:bg-slate-300 dark:hover:bg-slate-600'
                      )}
                    >
                      {index + 1}
                    </button>
                  ))}
                </div>

                <button
                  onClick={() => navigateToTask(Math.min((setDetails?.tasks?.length || 1) - 1, currentTaskIndex + 1))}
                  disabled={currentTaskIndex === (setDetails?.tasks?.length || 1) - 1}
                  className={cn(
                    'flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    currentTaskIndex === (setDetails?.tasks?.length || 1) - 1
                      ? 'text-muted-foreground cursor-not-allowed'
                      : 'text-foreground hover:bg-slate-200 dark:hover:bg-slate-700'
                  )}
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            )}

            {/* Content */}
            <div className="p-4 sm:p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                  <span className="ml-3 text-slate-600 dark:text-slate-400">
                    Loading set details...
                  </span>
                </div>
              ) : loadingTasks ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                  <span className="ml-3 text-slate-600 dark:text-slate-400">
                    Loading questions...
                  </span>
                </div>
              ) : error ? (
                <div className="flex items-center justify-center py-12 text-red-500">
                  <AlertCircle className="w-8 h-8" />
                  <span className="ml-3">{error}</span>
                </div>
              ) : currentTask ? (
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentTaskIndex}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <QuestionEditForm
                      questionData={currentTask}
                      onSave={handleSaveQuestion}
                      onCancel={handleClose}
                      onSaveSuccess={refreshCurrentTask}
                      saving={saving}
                    />
                  </motion.div>
                </AnimatePresence>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  No questions available for this content set.
                </div>
              )}
            </div>
          </motion.div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

export default SetEditModal
