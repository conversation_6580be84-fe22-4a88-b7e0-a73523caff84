import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Clock, Users, BookOpen, Star, MessageCircle, Edit3, Eye } from 'lucide-react'
import { ContentSet } from '../../services/curatedService'
import { cn } from '../../utils/cn'
import { formatLocalDate } from '../../utils/dateTimeHelper'

export interface ContentCardProps {
  content: ContentSet
  onClick?: (content: ContentSet) => void
  onEdit?: (content: ContentSet) => void
  className?: string
  showTheme?: boolean
}

/**
 * ContentCard Component
 * Displays a content set with theme information, difficulty, and status
 * Used in Anthology page and theme-specific views
 */
const ContentCard: React.FC<ContentCardProps> = React.memo(({
  content,
  onClick,
  onEdit,
  className,
  showTheme = true
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)

  const handleClick = () => {
    onClick?.(content)
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEdit?.(content)
  }

  // Auto slideshow for engagement questions
  useEffect(() => {
    if (content.engagement_questions && content.engagement_questions.length > 1) {
      const interval = setInterval(() => {
        setCurrentQuestionIndex((prev) =>
          (prev + 1) % content.engagement_questions!.length
        )
      }, 3000) // Change every 3 seconds

      return () => clearInterval(interval)
    }
  }, [content.engagement_questions])

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'text-green-600 bg-green-50 border-green-200'
      case 2: return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 3: return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50'
      case 'in_progress': return 'text-blue-600 bg-blue-50'
      case 'pending': return 'text-yellow-600 bg-yellow-50'
      case 'cancelled': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getDifficultyStars = (level: number) => {
    return Array.from({ length: 3 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'w-3 h-3',
          i < level ? 'fill-current text-yellow-400' : 'text-gray-300'
        )}
      />
    ))
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.2 }}
      className={cn(
        'bg-card border border-border rounded-lg p-4 cursor-pointer',
        'hover:shadow-lg hover:border-primary/20 transition-all duration-200',
        'group relative overflow-hidden',
        className
      )}
      onClick={handleClick}
    >
      {/* Theme indicator stripe */}
      {showTheme && content.theme && (
        <div 
          className="absolute top-0 left-0 w-full h-1 rounded-t-lg"
          style={{ backgroundColor: content.theme.color }}
        />
      )}

      {/* Header with theme info */}
      {showTheme && content.theme && (
        <div className="flex items-center gap-2 mb-3">
          <span className="text-lg">{content.theme.icon}</span>
          <span className="text-sm text-muted-foreground font-medium">
            {content.theme.name_en}
          </span>
        </div>
      )}

      {/* Content title and description */}
      <div className="space-y-2 mb-4">
        <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
          {content.title || content.title_en}
        </h3>
        <p className="text-sm text-muted-foreground line-clamp-2">
          {content.description || content.description_en}
        </p>
      </div>

      {/* Engagement Questions Slideshow */}
      {content.engagement_questions && content.engagement_questions.length > 0 && (
        <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 rounded-lg border border-blue-200/50 dark:border-blue-800/30">
          <div className="flex items-start gap-2">
            <MessageCircle className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <motion.p
                key={currentQuestionIndex}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
                className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed"
              >
                {content.engagement_questions[currentQuestionIndex].text ||
                 content.engagement_questions[currentQuestionIndex].text_en}
              </motion.p>
              {content.engagement_questions.length > 1 && (
                <div className="flex gap-1 mt-2">
                  {content.engagement_questions.map((_, index) => (
                    <div
                      key={index}
                      className={cn(
                        'w-1.5 h-1.5 rounded-full transition-colors duration-300',
                        index === currentQuestionIndex
                          ? 'bg-blue-600 dark:bg-blue-400'
                          : 'bg-blue-300 dark:bg-blue-600'
                      )}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="space-y-3">
        {/* Difficulty and items count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            {getDifficultyStars(content.difficulty_level)}
            <span className="text-xs text-muted-foreground ml-1">
              Level {content.difficulty_level}
            </span>
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <BookOpen className="w-3 h-3" />
            <span>{content.total_items} items</span>
          </div>
        </div>

        {/* Status and type */}
        <div className="flex items-center justify-between">
          <span className={cn(
            'px-2 py-1 rounded-full text-xs font-medium',
            getStatusColor(content.status)
          )}>
            {content.status.replace('_', ' ')}
          </span>
          <span className="text-xs text-muted-foreground capitalize">
            {content.gentype}
          </span>
        </div>

        {/* Created date */}
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Clock className="w-3 h-3" />
          <span>{formatLocalDate(content.created_at)}</span>
        </div>
      </div>

      {/* Action buttons - shown on hover */}
      <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <button
          onClick={handleClick}
          className="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg shadow-lg transition-colors duration-200"
          title="View Questions"
        >
          <Eye className="w-4 h-4" />
        </button>
        {onEdit && (
          <button
            onClick={handleEdit}
            className="p-2 bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-lg transition-colors duration-200"
            title="Edit Questions"
          >
            <Edit3 className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none" />
    </motion.div>
  )
})

ContentCard.displayName = 'ContentCard'

export default ContentCard
