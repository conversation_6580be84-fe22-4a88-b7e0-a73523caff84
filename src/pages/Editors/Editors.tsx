import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { BookO<PERSON>, Palette, Zap, ArrowRight, Users, FileText, TrendingUp } from 'lucide-react'
import MainLayout from '../../components/layout/MainLayout'
import { cn } from '../../utils/cn'

/**
 * Editors Landing Page
 * Default curated content page that shows when users click on "Editors"
 * Provides overview and navigation to Anthology, Atelier, and Playground sections
 */
const Editors: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  const editorSections = [
    {
      path: '/themes',
      title: 'Themes',
      description: 'Creative workspace for content themes and exploration',
      icon: Palette,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-950/20',
      stats: { themes: '40+', activities: '200+' }
    },
    {
      path: '/editors-pick',
      title: 'Editors Pick',
      description: 'Curated collections of learning content and stories',
      icon: BookOpen,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20',
      stats: { items: '150+', collections: '25+' }
    },
    {
      path: '/editor-chamber',
      title: 'Editor Chamber',
      description: 'Content creation and management workspace',
      icon: Zap,
      color: 'from-emerald-500 to-emerald-600',
      bgColor: 'bg-emerald-50 dark:bg-emerald-950/20',
      stats: { tools: '2', features: 'Advanced' }
    }
  ]

  return (
    <MainLayout
      title="🎨 Editors Chamber"
      description="Curated content creation and management workspace"
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-6xl mx-auto space-y-6"
      >
        {/* Welcome Section */}
        <motion.div
          variants={cardVariants}
          className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900/50 dark:to-slate-800/50 rounded-xl p-6 lg:p-8 border border-border"
        >
          <div className="text-center space-y-4">
            <h1 className="text-2xl lg:text-3xl font-bold text-foreground">
              Welcome to Editors Chamber
            </h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Your central hub for creating, managing, and exploring curated learning content. 
              Choose a section below to get started.
            </p>
          </div>
        </motion.div>

        {/* Stats Overview */}
        <motion.div
          variants={cardVariants}
          className="grid grid-cols-1 md:grid-cols-3 gap-4"
        >
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-foreground">365+</div>
            <div className="text-sm text-muted-foreground">Total Content Items</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="text-2xl font-bold text-foreground">65+</div>
            <div className="text-sm text-muted-foreground">Active Themes</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div className="text-2xl font-bold text-foreground">1.2k+</div>
            <div className="text-sm text-muted-foreground">Generated Items</div>
          </div>
        </motion.div>

        {/* Editor Sections */}
        <motion.div
          variants={containerVariants}
          className="grid grid-cols-1 lg:grid-cols-3 gap-6"
        >
          {editorSections.map((section) => (
            <motion.div
              key={section.path}
              variants={cardVariants}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link to={section.path} className="block h-full">
                <div className={cn(
                  "h-full bg-card border border-border rounded-xl p-6 transition-all duration-200",
                  "hover:shadow-lg hover:border-primary/20"
                )}>
                  {/* Header */}
                  <div className="flex items-center gap-3 mb-4">
                    <div className={cn(
                      "p-3 rounded-lg bg-gradient-to-br",
                      section.color
                    )}>
                      <section.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-foreground">
                        {section.title}
                      </h3>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-muted-foreground text-sm mb-6 leading-relaxed">
                    {section.description}
                  </p>

                  {/* Stats */}
                  <div className={cn("rounded-lg p-3 mb-4", section.bgColor)}>
                    <div className="grid grid-cols-2 gap-2 text-center">
                      {Object.entries(section.stats).map(([key, value]) => (
                        <div key={key}>
                          <div className="text-lg font-bold text-foreground">{value}</div>
                          <div className="text-xs text-muted-foreground capitalize">{key}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Action */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-primary">
                      Explore {section.title}
                    </span>
                    <ArrowRight className="h-4 w-4 text-primary" />
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          variants={cardVariants}
          className="bg-card border border-border rounded-xl p-6"
        >
          <h2 className="text-lg font-semibold text-foreground mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Link
              to="/themes"
              className="flex items-center gap-3 p-3 rounded-lg bg-purple-50 dark:bg-purple-950/20 hover:bg-purple-100 dark:hover:bg-purple-950/30 transition-colors"
            >
              <Palette className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              <span className="text-sm font-medium text-foreground">Explore Themes</span>
            </Link>
            <Link
              to="/editors-pick"
              className="flex items-center gap-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 hover:bg-blue-100 dark:hover:bg-blue-950/30 transition-colors"
            >
              <BookOpen className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-foreground">Browse Collections</span>
            </Link>
            <Link
              to="/editor-chamber"
              className="flex items-center gap-3 p-3 rounded-lg bg-emerald-50 dark:bg-emerald-950/20 hover:bg-emerald-100 dark:hover:bg-emerald-950/30 transition-colors"
            >
              <Zap className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
              <span className="text-sm font-medium text-foreground">Create Content</span>
            </Link>
          </div>
        </motion.div>
      </motion.div>
    </MainLayout>
  )
}

export default Editors
