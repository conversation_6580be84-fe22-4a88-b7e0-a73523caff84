import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  FileQuestion,
  <PERSON>lette,
  ChevronLeft,
  ChevronRight,
  RotateCcw
} from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import QuestionModal from '../../../components/curated/QuestionModal'
import { cn } from '../../../utils/cn'
import {
  CuratedService,
  ThemeQuestion,
  ThemeQuestionsQuery,
  Theme,
  PaginatedResponse
} from '../../../services/curatedService'

/**
 * Daily Questions Page
 * Manage theme-based daily questions with pagination and filtering
 */
const DailyQuestions: React.FC = () => {
  const [questions, setQuestions] = useState<ThemeQuestion[]>([])
  const [themes, setThemes] = useState<Theme[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTheme, setSelectedTheme] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingQuestion, setEditingQuestion] = useState<ThemeQuestion | null>(null)

  const itemsPerPage = 10

  // Fetch questions with current filters
  const fetchQuestions = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params: ThemeQuestionsQuery = {
        page: currentPage,
        limit: itemsPerPage,
        sort_by: 'created_at',
        sort_order: 'desc'
      }

      if (searchTerm.trim()) {
        params.search = searchTerm.trim()
      }

      if (selectedTheme) {
        params.theme_id = selectedTheme
      }

      const response: PaginatedResponse<ThemeQuestion> = await CuratedService.getThemeQuestions(params)
      
      setQuestions(response.data)
      setTotalPages(response.meta.total_pages)
      setTotalItems(response.meta.total)
    } catch (err) {
      console.error('Error fetching questions:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch questions')
      setQuestions([])
    } finally {
      setLoading(false)
    }
  }, [currentPage, searchTerm, selectedTheme])

  // Fetch themes for filter dropdown
  const fetchThemes = useCallback(async () => {
    try {
      const response = await CuratedService.getThemes({ limit: 100, is_active: true })
      setThemes(response.data)
    } catch (err) {
      console.error('Error fetching themes:', err)
    }
  }, [])

  // Load data on component mount and when filters change
  useEffect(() => {
    fetchQuestions()
  }, [fetchQuestions])

  useEffect(() => {
    fetchThemes()
  }, [fetchThemes])

  // Handle search
  const handleSearch = useCallback((value: string) => {
    setSearchTerm(value)
    setCurrentPage(1) // Reset to first page when searching
  }, [])

  // Handle theme filter
  const handleThemeFilter = useCallback((themeId: string) => {
    setSelectedTheme(themeId)
    setCurrentPage(1) // Reset to first page when filtering
  }, [])

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSearchTerm('')
    setSelectedTheme('')
    setCurrentPage(1)
  }, [])

  // Handle pagination
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])

  // Handle create question
  const handleCreateQuestion = useCallback(() => {
    setEditingQuestion(null)
    setShowCreateModal(true)
  }, [])

  // Handle edit question
  const handleEditQuestion = useCallback((question: ThemeQuestion) => {
    setEditingQuestion(question)
    setShowCreateModal(true)
  }, [])

  // Handle delete question
  const handleDeleteQuestion = useCallback(async (questionId: string) => {
    if (!confirm('Are you sure you want to delete this question?')) {
      return
    }

    try {
      await CuratedService.deleteThemeQuestion(questionId)
      await fetchQuestions() // Refresh the list
    } catch (err) {
      console.error('Error deleting question:', err)
      alert('Failed to delete question')
    }
  }, [fetchQuestions])

  // Get theme name by ID
  const getThemeName = useCallback((themeId: string) => {
    const theme = themes.find(t => t._id === themeId || t.id === themeId)
    return theme?.name_en || theme?.name || 'Unknown Theme'
  }, [themes])

  // Get theme colors by ID
  const getThemeColors = useCallback((themeId: string) => {
    const theme = themes.find(t => t._id === themeId || t.id === themeId)
    return {
      background: theme?.background_color || '#3b82f6',
      font: theme?.font_color || '#ffffff'
    }
  }, [themes])

  return (
    <MainLayout
      title="📝 Daily Questions"
      description="Manage theme-based daily questions"
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 p-6 border-b border-border">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <FileQuestion className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">Daily Questions</h1>
                <p className="text-muted-foreground">
                  Manage theme-based questions • {totalItems} total questions
                </p>
              </div>
            </div>
            
            <button
              onClick={handleCreateQuestion}
              className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              <Plus className="w-4 h-4" />
              Add Question
            </button>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search questions..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
              />
            </div>

            {/* Theme Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <select
                value={selectedTheme}
                onChange={(e) => handleThemeFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary min-w-[200px]"
              >
                <option value="">All Themes</option>
                {themes.map((theme) => (
                  <option key={theme._id || theme.id} value={theme._id || theme.id}>
                    {theme.name_en || theme.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Clear Filters */}
            {(searchTerm || selectedTheme) && (
              <button
                onClick={clearFilters}
                className="flex items-center gap-2 px-3 py-2 text-muted-foreground hover:text-foreground border border-border rounded-lg hover:bg-accent transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                Clear
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading questions...</p>
              </div>
            </div>
          ) : error ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <FileQuestion className="w-8 h-8 text-red-600 dark:text-red-400" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">Error Loading Questions</h3>
                <p className="text-muted-foreground mb-4">{error}</p>
                <button
                  onClick={fetchQuestions}
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : questions.length === 0 ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <FileQuestion className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No Questions Found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || selectedTheme 
                    ? 'Try adjusting your filters or search terms'
                    : 'Get started by creating your first question'
                  }
                </p>
                <button
                  onClick={handleCreateQuestion}
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Question
                </button>
              </div>
            </div>
          ) : (
            <div className="h-full flex flex-col">
              {/* Questions List */}
              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-4">
                  <AnimatePresence>
                    {questions.map((question, index) => {
                      const themeColors = getThemeColors(question.theme_id)
                      
                      return (
                        <motion.div
                          key={question._id || question.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ delay: index * 0.05 }}
                          className="bg-card border border-border rounded-lg p-4 hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              {/* Theme Badge */}
                              <div className="flex items-center gap-2 mb-3">
                                <div 
                                  className="flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium"
                                  style={{
                                    backgroundColor: `${themeColors.background}20`,
                                    color: themeColors.background
                                  }}
                                >
                                  <Palette className="w-3 h-3" />
                                  {getThemeName(question.theme_id)}
                                </div>
                              </div>

                              {/* Question Text */}
                              <div className="space-y-2">
                                <p className="text-foreground font-medium">
                                  {question.text}
                                </p>
                                {question.text_en && (
                                  <p className="text-muted-foreground text-sm">
                                    {question.text_en}
                                  </p>
                                )}
                              </div>

                              {/* Metadata */}
                              <div className="mt-3 text-xs text-muted-foreground">
                                Created {new Date(question.created_at).toLocaleDateString()}
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="flex items-center gap-2 ml-4">
                              <button
                                onClick={() => handleEditQuestion(question)}
                                className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                                title="Edit question"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteQuestion(question._id || question.id!)}
                                className="p-2 text-muted-foreground hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors"
                                title="Delete question"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </motion.div>
                      )
                    })}
                  </AnimatePresence>
                </div>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex-shrink-0 p-6 border-t border-border">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground">
                      Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} questions
                    </p>
                    
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="p-2 text-muted-foreground hover:text-foreground disabled:opacity-50 disabled:cursor-not-allowed hover:bg-accent rounded-lg transition-colors"
                      >
                        <ChevronLeft className="w-4 h-4" />
                      </button>
                      
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          const page = i + 1
                          return (
                            <button
                              key={page}
                              onClick={() => handlePageChange(page)}
                              className={cn(
                                'px-3 py-1 text-sm rounded-lg transition-colors',
                                page === currentPage
                                  ? 'bg-primary text-primary-foreground'
                                  : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                              )}
                            >
                              {page}
                            </button>
                          )
                        })}
                      </div>
                      
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="p-2 text-muted-foreground hover:text-foreground disabled:opacity-50 disabled:cursor-not-allowed hover:bg-accent rounded-lg transition-colors"
                      >
                        <ChevronRight className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Question Modal */}
      <QuestionModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={fetchQuestions}
        editingQuestion={editingQuestion}
      />
    </MainLayout>
  )
}

export default DailyQuestions
