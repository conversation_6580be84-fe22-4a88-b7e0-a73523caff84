import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { Zap, FileQuestion, ChevronLeft, ChevronRight, Edit } from 'lucide-react'
import { cn } from '../../../utils/cn'
import MainLayout from '../../../components/layout/MainLayout'

/**
 * Editor Chamber Page with Side Navigation
 * Main page for Editor Chamber with sub-navigation to different sections
 */
const EditorChamber: React.FC = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  const location = useLocation()

  const navigationItems = [
    {
      path: '/editor-chamber/create-content',
      label: 'Create Content',
      icon: Zap,
      description: 'AI content generation and prompt editor'
    },
    {
      path: '/editor-chamber/daily-questions',
      label: 'Daily Questions',
      icon: FileQuestion,
      description: 'Manage theme-based daily questions'
    }
  ]

  const isActive = (path: string) => {
    return location.pathname === path
  }

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed)
  }

  return (
    <MainLayout 
      title="⚡ Editor Chamber" 
      description="Content creation and management workspace"
    >
      <div className="flex h-full">
        {/* Side Navigation */}
        <motion.div
          initial={false}
          animate={{
            width: isSidebarCollapsed ? '60px' : '280px'
          }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="relative bg-card border-r border-border flex-shrink-0"
        >
          {/* Sidebar Content */}
          <div className="h-full flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-border">
              <AnimatePresence>
                {!isSidebarCollapsed && (
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <h2 className="text-lg font-semibold text-foreground">
                      Editor Chamber
                    </h2>
                    <p className="text-sm text-muted-foreground">
                      Content creation tools
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Navigation Items */}
            <nav className="flex-1 p-2">
              <div className="space-y-1">
                {navigationItems.map((item) => {
                  const active = isActive(item.path)
                  
                  return (
                    <Link
                      key={item.path}
                      to={item.path}
                      className="block"
                    >
                      <motion.div
                        className={cn(
                          'flex items-center gap-3 p-3 rounded-lg transition-all duration-200',
                          'hover:bg-accent hover:text-accent-foreground',
                          active && 'bg-primary/10 text-primary border border-primary/20',
                          !active && 'text-muted-foreground hover:text-foreground',
                          isSidebarCollapsed && 'justify-center px-2'
                        )}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <item.icon className={cn(
                          'flex-shrink-0',
                          isSidebarCollapsed ? 'w-5 h-5' : 'w-5 h-5'
                        )} />
                        
                        <AnimatePresence>
                          {!isSidebarCollapsed && (
                            <motion.div
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -10 }}
                              transition={{ duration: 0.2 }}
                              className="flex-1 min-w-0"
                            >
                              <div className="font-medium text-sm">
                                {item.label}
                              </div>
                              <div className="text-xs text-muted-foreground truncate">
                                {item.description}
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    </Link>
                  )
                })}
              </div>
            </nav>
          </div>

          {/* Toggle Button */}
          <button
            onClick={toggleSidebar}
            className={cn(
              'absolute top-4 z-10 p-1.5 rounded-full transition-all duration-300',
              'bg-background border border-border shadow-md hover:shadow-lg',
              'hover:bg-accent text-foreground hover:scale-110',
              isSidebarCollapsed ? '-right-4' : '-right-4'
            )}
            title={isSidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isSidebarCollapsed ? (
              <ChevronRight className="h-3 w-3" />
            ) : (
              <ChevronLeft className="h-3 w-3" />
            )}
          </button>
        </motion.div>

        {/* Main Content */}
        <div className="flex-1 min-w-0 p-6">
          <div className="h-full flex items-center justify-center">
            <div className="text-center max-w-2xl">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6 mx-auto">
                <Edit className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-foreground mb-4">
                Welcome to Editor Chamber
              </h1>
              <p className="text-lg text-muted-foreground mb-8">
                Your central hub for content creation and management. Choose a section from the sidebar to get started.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {navigationItems.map((item) => (
                  <Link
                    key={item.path}
                    to={item.path}
                    className="p-6 bg-card border border-border rounded-xl hover:shadow-lg transition-all duration-200 hover:border-primary/20 group"
                  >
                    <div className="flex items-center gap-4 mb-3">
                      <div className="p-3 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                        <item.icon className="w-6 h-6 text-primary" />
                      </div>
                      <h3 className="text-lg font-semibold text-foreground">
                        {item.label}
                      </h3>
                    </div>
                    <p className="text-muted-foreground text-left">
                      {item.description}
                    </p>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}

export default EditorChamber
