import React from 'react'
import { <PERSON>f<PERSON><PERSON><PERSON>, Filter, ChevronDown, X, Book<PERSON>pen, Tag, Zap, BarChart3 } from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import {
  ContentCard,
  SearchBar,
  LoadingGrid,
  EmptyState
} from '../../../components/curated'
import { ContentSet, FilterOptions } from '../../../services/curatedService'
import { ContentSetFilter } from '../pages/curated.anthology'
import { cn } from '../../../utils/cn'

interface AnthologyComponentProps {
  loading: boolean
  content: ContentSet[]
  error: string | null
  filter: ContentSetFilter
  filterOptions: FilterOptions | null
  loadingFilters: boolean
  totalItems: number
  totalPages: number
  onFilterChange: (filter: ContentSetFilter) => void
  onPageChange: (page: number) => void
  onPageSizeChange: (limit: number) => void
  onContentClick: (content: ContentSet) => void
  onContentEdit: (content: ContentSet) => void
  onRefresh: () => void
}

/**
 * Anthology Component - Pure UI component for curated content list
 */
const AnthologyComponent: React.FC<AnthologyComponentProps> = ({
  loading,
  content,
  error,
  filter,
  filterOptions,
  loadingFilters,
  totalItems,
  totalPages,
  onFilterChange,
  onPageChange,
  onPageSizeChange,
  onContentClick,
  onContentEdit,
  onRefresh
}) => {
  // Check if there are active filters
  const hasActiveFilters = Object.entries(filter).some(([key, value]) => {
    if (key === 'page' || key === 'limit') return false
    return value !== undefined && value !== ''
  })

  // Handle individual filter changes
  const handleFilterFieldChange = (key: string, value: string) => {
    const newFilter: any = { ...filter, page: 1 }

    // Only set the key if value is not empty, otherwise remove it completely
    if (value && value.trim() !== '') {
      if (key === 'difficulty_level') {
        newFilter[key] = parseInt(value)
      } else {
        newFilter[key] = value
      }
    } else {
      // Remove the key completely from the filter object
      delete newFilter[key]
    }

    onFilterChange(newFilter)
  }

  const handleSearch = (searchTerm: string) => {
    const newFilter: any = { ...filter, page: 1 }

    // Only set search if it has a value, otherwise remove it
    if (searchTerm && searchTerm.trim() !== '') {
      newFilter.search = searchTerm.trim()
    } else {
      delete newFilter.search
    }

    onFilterChange(newFilter)
  }

  const handleClearFilters = () => {
    onFilterChange({
      page: 1,
      limit: filter.limit
    })
  }

  // Top content (search and filters)
  const topContent = (
    <div className="space-y-2">
      {/* Search bar */}
      <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <SearchBar
            placeholder="Search content sets..."
            value={filter.search || ''}
            onChange={handleSearch}
            className="w-full"
          />
        </div>
        
        {/* Active filters indicator and clear button */}
        {hasActiveFilters && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {Object.entries(filter).filter(([key, value]) => {
                if (key === 'page' || key === 'limit') return false
                return value !== undefined && value !== ''
              }).length} filters active
            </span>
            <button
              onClick={handleClearFilters}
              className="text-sm text-primary hover:text-primary/80 transition-colors"
            >
              Clear all
            </button>
          </div>
        )}

        {/* Refresh button */}
        <button
          onClick={onRefresh}
          disabled={loading}
          className={cn(
            'p-2 rounded-lg border border-border text-muted-foreground',
            'hover:text-foreground hover:border-primary/50 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-primary/20',
            'disabled:opacity-50 disabled:cursor-not-allowed'
          )}
        >
          <RefreshCw className={cn('w-4 h-4', loading && 'animate-spin')} />
        </button>
      </div>

      {/* Compact Filter Panel */}
      {!loadingFilters && filterOptions && (
        <div className="bg-gradient-to-r from-blue-50/50 via-indigo-50/50 to-purple-50/50 dark:from-blue-950/20 dark:via-indigo-950/20 dark:to-purple-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/30 p-3 sm:p-4 backdrop-blur-sm">
          <div className="flex items-center gap-2 mb-3">
            <div className="p-1.5 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-md">
              <Filter className="w-3 h-3 text-white" />
            </div>
            <h3 className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Filter Content
            </h3>
            <BookOpen className="w-3 h-3 text-blue-500" />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            {/* Theme Filter */}
            <div className="space-y-1.5">
              <label className="flex items-center gap-1.5 text-xs font-medium text-foreground">
                <Tag className="w-3 h-3 text-blue-500" />
                Theme
              </label>
              <div className="relative">
                <select
                  value={filter.theme_id || ''}
                  onChange={(e) => handleFilterFieldChange('theme_id', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 rounded-lg border border-blue-200/50 dark:border-blue-800/30',
                    'bg-white/80 dark:bg-gray-900/80 text-foreground text-xs backdrop-blur-sm',
                    'focus:outline-none focus:ring-1 focus:ring-blue-500/30 focus:border-blue-500/50',
                    'hover:border-blue-300/70 dark:hover:border-blue-700/50',
                    'transition-all duration-200',
                    'appearance-none cursor-pointer'
                  )}
                >
                  <option value="">🎯 All Themes</option>
                  {filterOptions.themes.map((theme) => (
                    <option key={theme.id} value={theme.id}>
                      🎨 {theme.name_en}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-blue-400 pointer-events-none" />
              </div>
            </div>

            {/* Status Filter */}
            <div className="space-y-1.5">
              <label className="flex items-center gap-1.5 text-xs font-medium text-foreground">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                Status
              </label>
              <div className="relative">
                <select
                  value={filter.status || ''}
                  onChange={(e) => handleFilterFieldChange('status', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 rounded-lg border border-indigo-200/50 dark:border-indigo-800/30',
                    'bg-white/80 dark:bg-gray-900/80 text-foreground text-xs backdrop-blur-sm',
                    'focus:outline-none focus:ring-1 focus:ring-indigo-500/30 focus:border-indigo-500/50',
                    'hover:border-indigo-300/70 dark:hover:border-indigo-700/50',
                    'transition-all duration-200',
                    'appearance-none cursor-pointer'
                  )}
                >
                  <option value="">📊 All Status</option>
                  {['published', 'draft', 'archived'].map((status) => (
                    <option key={status} value={status}>
                      {status === 'published' ? '✅' : status === 'draft' ? '📝' : '📦'} {status.charAt(0).toUpperCase() + status.slice(1)}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-indigo-400 pointer-events-none" />
              </div>
            </div>

            {/* Generation Type Filter */}
            <div className="space-y-1.5">
              <label className="flex items-center gap-1.5 text-xs font-medium text-foreground">
                <Zap className="w-3 h-3 text-purple-500" />
                Type
              </label>
              <div className="relative">
                <select
                  value={filter.gentype || ''}
                  onChange={(e) => handleFilterFieldChange('gentype', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 rounded-lg border border-purple-200/50 dark:border-purple-800/30',
                    'bg-white/80 dark:bg-gray-900/80 text-foreground text-xs backdrop-blur-sm',
                    'focus:outline-none focus:ring-1 focus:ring-purple-500/30 focus:border-purple-500/50',
                    'hover:border-purple-300/70 dark:hover:border-purple-700/50',
                    'transition-all duration-200',
                    'appearance-none cursor-pointer'
                  )}
                >
                  <option value="">⚡ All Types</option>
                  {['primary', 'secondary', 'ai_generated'].map((gentype) => (
                    <option key={gentype} value={gentype}>
                      {gentype === 'ai_generated' ? '🤖' : gentype === 'primary' ? '⭐' : '🔧'} {gentype.replace('_', ' ').charAt(0).toUpperCase() + gentype.replace('_', ' ').slice(1)}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-purple-400 pointer-events-none" />
              </div>
            </div>

            {/* Difficulty Filter */}
            <div className="space-y-1.5">
              <label className="flex items-center gap-1.5 text-xs font-medium text-foreground">
                <BarChart3 className="w-3 h-3 text-orange-500" />
                Difficulty
              </label>
              <div className="relative">
                <select
                  value={filter.difficulty_level || ''}
                  onChange={(e) => handleFilterFieldChange('difficulty_level', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 rounded-lg border border-orange-200/50 dark:border-orange-800/30',
                    'bg-white/80 dark:bg-gray-900/80 text-foreground text-xs backdrop-blur-sm',
                    'focus:outline-none focus:ring-1 focus:ring-orange-500/30 focus:border-orange-500/50',
                    'hover:border-orange-300/70 dark:hover:border-orange-700/50',
                    'transition-all duration-200',
                    'appearance-none cursor-pointer'
                  )}
                >
                  <option value="">📈 All Levels</option>
                  <option value="1">🟢 Level 1</option>
                  <option value="2">🟡 Level 2</option>
                  <option value="3">🔴 Level 3</option>
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-orange-400 pointer-events-none" />
              </div>
            </div>
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="mt-3 pt-2 border-t border-blue-200/30 dark:border-blue-800/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <span className="text-xs font-medium text-blue-600 dark:text-blue-400">Active Filters:</span>
                  <div className="flex gap-2 flex-wrap">
                    {filter.search && (
                      <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
                        Search: "{filter.search}"
                      </span>
                    )}
                    {filter.theme_id && (
                      <span className="px-3 py-1 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300 rounded-full text-xs font-medium">
                        Theme: {filterOptions.themes.find(t => t.id === filter.theme_id)?.name_en || filter.theme_id}
                      </span>
                    )}
                    {filter.status && (
                      <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium">
                        Status: {filter.status}
                      </span>
                    )}
                    {filter.gentype && (
                      <span className="px-3 py-1 bg-orange-100 dark:bg-orange-900/50 text-orange-700 dark:text-orange-300 rounded-full text-xs font-medium">
                        Type: {filter.gentype}
                      </span>
                    )}
                    {filter.difficulty_level && (
                      <span className="px-3 py-1 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 rounded-full text-xs font-medium">
                        Level: {filter.difficulty_level}
                      </span>
                    )}
                  </div>
                </div>
                <button
                  onClick={handleClearFilters}
                  className="flex items-center gap-1 px-3 py-1 text-xs font-medium text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                >
                  <X className="w-3 h-3" />
                  Clear All
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )

  // Bottom content (pagination)
  const bottomContent = !loading && !error && content.length > 0 ? (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
      {/* Page Size Selector */}
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">Show:</span>
        <select
          value={filter.limit}
          onChange={(e) => onPageSizeChange(parseInt(e.target.value))}
          className="px-3 py-1.5 text-sm border border-border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary/20"
        >
          <option value={12}>12 per page</option>
          <option value={20}>20 per page</option>
          <option value={36}>36 per page</option>
          <option value={48}>48 per page</option>
        </select>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center gap-2">
          <button
            onClick={() => onPageChange(filter.page - 1)}
            disabled={filter.page <= 1}
            className="px-3 py-2 text-sm border border-border rounded-lg hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          <span className="px-4 py-2 text-sm text-muted-foreground">
            Page {filter.page} of {totalPages}
          </span>

          <button
            onClick={() => onPageChange(filter.page + 1)}
            disabled={filter.page >= totalPages}
            className="px-3 py-2 text-sm border border-border rounded-lg hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}

      {/* Results Info */}
      <div className="text-sm text-muted-foreground">
        Showing {((filter.page - 1) * filter.limit) + 1} to {Math.min(filter.page * filter.limit, totalItems)} of {totalItems} results
      </div>
    </div>
  ) : null

  return (
    <MainLayout
      title="📚 Editors Pick"
      description="Discover and explore curated content collections"
      topContent={topContent}
      bottomContent={bottomContent}
    >
      {/* Content Area */}
      <div className="p-4 lg:p-6">
      {loading ? (
        <LoadingGrid count={8} type="content" />
      ) : error ? (
        <EmptyState
          type="error"
          title="Failed to load content"
          description={error}
          actionLabel="Try again"
          onAction={onRefresh}
        />
      ) : content.length === 0 ? (
        <EmptyState
          type={hasActiveFilters ? 'search' : 'content'}
          actionLabel={hasActiveFilters ? 'Clear filters' : 'Refresh'}
          onAction={hasActiveFilters ? handleClearFilters : onRefresh}
        />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {content.map((item) => (
            <ContentCard
              key={item._id}
              content={item}
              onClick={onContentClick}
              onEdit={onContentEdit}
              showTheme={true}
            />
          ))}
        </div>
      )}
      </div>
    </MainLayout>
  )
}

export default AnthologyComponent
